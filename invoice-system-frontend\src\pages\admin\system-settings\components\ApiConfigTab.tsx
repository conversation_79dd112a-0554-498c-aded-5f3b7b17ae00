import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Globe,
  MapPin,
  Save,
  Eye,
  EyeOff,
  AlertCircle,
  Briefcase,
  Settings,
  Shield,
  Link,
  Database
} from 'lucide-react';
import { SystemConfigData, RegionMapping } from '@/services/systemConfigService';
import { MappingConfigCard } from './MappingConfigCard';

interface ServiceTypeMapping {
  code: string;
  name: string;
}

interface PreferentialPolicyMapping {
  code: string;
  name: string;
}

interface ApiConfigTabProps {
  config: SystemConfigData;
  setConfig: React.Dispatch<React.SetStateAction<SystemConfigData>>;
  regionMappings: RegionMapping[];
  setRegionMappings: React.Dispatch<React.SetStateAction<RegionMapping[]>>;
  newRegion: { region: string; code: string };
  setNewRegion: React.Dispatch<React.SetStateAction<{ region: string; code: string }>>;
  editingIndex: number | null;
  setEditingIndex: React.Dispatch<React.SetStateAction<number | null>>;
  showPassword: boolean;
  setShowPassword: React.Dispatch<React.SetStateAction<boolean>>;
  saving: boolean;
  onSave: () => void;
  onAddRegionMapping: () => void;
  onDeleteRegionMapping: (index: number) => void;
  onEditRegionMapping: (index: number) => void;
  onSaveRegionMapping: (index: number, region: string, code: string) => void;
  // 新增业务类型相关props
  serviceTypeMappings: ServiceTypeMapping[];
  setServiceTypeMappings: React.Dispatch<React.SetStateAction<ServiceTypeMapping[]>>;
  newServiceType: { code: string; name: string };
  setNewServiceType: React.Dispatch<React.SetStateAction<{ code: string; name: string }>>;
  editingServiceIndex: number | null;
  setEditingServiceIndex: React.Dispatch<React.SetStateAction<number | null>>;
  onAddServiceTypeMapping: () => void;
  onDeleteServiceTypeMapping: (index: number) => void;
  onEditServiceTypeMapping: (index: number) => void;
  onSaveServiceTypeMapping: (index: number, code: string, name: string) => void;
  // 新增优惠政策类型相关props
  preferentialPolicyMappings: PreferentialPolicyMapping[];
  setPreferentialPolicyMappings: React.Dispatch<React.SetStateAction<PreferentialPolicyMapping[]>>;
  newPreferentialPolicy: { code: string; name: string };
  setNewPreferentialPolicy: React.Dispatch<React.SetStateAction<{ code: string; name: string }>>;
  editingPolicyIndex: number | null;
  setEditingPolicyIndex: React.Dispatch<React.SetStateAction<number | null>>;
  onAddPreferentialPolicyMapping: () => void;
  onDeletePreferentialPolicyMapping: (index: number) => void;
  onEditPreferentialPolicyMapping: (index: number) => void;
  onSavePreferentialPolicyMapping: (index: number, code: string, name: string) => void;
}

export const ApiConfigTab: React.FC<ApiConfigTabProps> = ({
  config,
  setConfig,
  regionMappings,
  setRegionMappings,
  newRegion,
  setNewRegion,
  editingIndex,
  setEditingIndex,
  showPassword,
  setShowPassword,
  saving,
  onSave,
  onAddRegionMapping,
  onDeleteRegionMapping,
  onEditRegionMapping,
  onSaveRegionMapping,
  // 新增业务类型相关props
  serviceTypeMappings,
  setServiceTypeMappings,
  newServiceType,
  setNewServiceType,
  editingServiceIndex,
  setEditingServiceIndex,
  onAddServiceTypeMapping,
  onDeleteServiceTypeMapping,
  onEditServiceTypeMapping,
  onSaveServiceTypeMapping,
  // 新增优惠政策类型相关props
  preferentialPolicyMappings,
  setPreferentialPolicyMappings,
  newPreferentialPolicy,
  setNewPreferentialPolicy,
  editingPolicyIndex,
  setEditingPolicyIndex,
  onAddPreferentialPolicyMapping,
  onDeletePreferentialPolicyMapping,
  onEditPreferentialPolicyMapping,
  onSavePreferentialPolicyMapping,
}) => {
  // 转换数据格式以适配通用组件
  const regionMappingItems = regionMappings.map(m => ({ name: m.region, code: m.code }));
  const serviceTypeMappingItems = serviceTypeMappings.map(m => ({ name: m.name, code: m.code }));
  const policyMappingItems = preferentialPolicyMappings.map(m => ({ name: m.name, code: m.code }));

  const setRegionMappingItems = React.useCallback((items: React.SetStateAction<Array<{ name: string; code: string }>>) => {
    if (typeof items === 'function') {
      setRegionMappings(prev => {
        const currentItems = prev.map(item => ({ name: item.region, code: item.code }));
        const newItems = items(currentItems);
        return newItems.map(item => ({ region: item.name, code: item.code }));
      });
    } else {
      setRegionMappings(items.map(item => ({ region: item.name, code: item.code })));
    }
  }, [setRegionMappings]);

  const setServiceTypeMappingItems = React.useCallback((items: React.SetStateAction<Array<{ name: string; code: string }>>) => {
    if (typeof items === 'function') {
      setServiceTypeMappings(prev => {
        const currentItems = prev.map(item => ({ name: item.name, code: item.code }));
        const newItems = items(currentItems);
        return newItems.map(item => ({ name: item.name, code: item.code }));
      });
    } else {
      setServiceTypeMappings(items.map(item => ({ name: item.name, code: item.code })));
    }
  }, [setServiceTypeMappings]);

  const setPolicyMappingItems = React.useCallback((items: React.SetStateAction<Array<{ name: string; code: string }>>) => {
    if (typeof items === 'function') {
      setPreferentialPolicyMappings(prev => {
        const currentItems = prev.map(item => ({ name: item.name, code: item.code }));
        const newItems = items(currentItems);
        return newItems.map(item => ({ name: item.name, code: item.code }));
      });
    } else {
      setPreferentialPolicyMappings(items.map(item => ({ name: item.name, code: item.code })));
    }
  }, [setPreferentialPolicyMappings]);

  // 预设数据
  const presetServiceTypes = [
    { name: '成品油', code: '2001' },
    { name: '不动产经营租赁服务', code: '2002' },
    { name: '货物运输服务', code: '2003' },
    { name: '旅客运输服务', code: '2004' }
  ];

  const presetPolicyTypes = [
    { name: '简易征收', code: '3001' },
    { name: '稀土产品', code: '3002' },
    { name: '免税', code: '3003' },
    { name: '不征税', code: '3004' },
    { name: '先征后退', code: '3005' },
    { name: '100%先征后退', code: '3006' },
    { name: '50%先征后退', code: '3007' },
    { name: '按3%简易征收', code: '3008' },
    { name: '按5%简易征收', code: '3009' },
    { name: '按5%简易征收减按1.5%计证', code: '3010' },
    { name: '超税负3%即征即退', code: '3015' }
  ];

  return (
    <div className="space-y-8">
      {/* 页面标题和描述 */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Settings className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">API配置管理</h2>
            <p className="text-gray-600">配置外部API接口和数据映射关系</p>
          </div>
        </div>
        <Separator className="my-4" />
      </div>

      {/* API配置 */}
      <Card className="shadow-sm border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Globe className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  外部API配置
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  配置与外部服务的API连接参数
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={config.apiUrl && config.aid && config.appSecret ? "default" : "secondary"}>
                {config.apiUrl && config.aid && config.appSecret ? "已配置" : "未配置"}
              </Badge>
              <Button onClick={onSave} disabled={saving} className="shadow-sm">
                <Save className="h-4 w-4 mr-2" />
                {saving ? '保存中...' : '保存配置'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 基础配置 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Link className="h-4 w-4 text-blue-600" />
              <h4 className="font-medium text-gray-900">基础连接配置</h4>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="apiUrl" className="text-sm font-medium text-gray-700">
                  API地址 (URL) <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="apiUrl"
                  value={config.apiUrl || ''}
                  onChange={(e) => setConfig(prev => ({ ...prev, apiUrl: e.target.value }))}
                  placeholder="https://api.example.com"
                  className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="aid" className="text-sm font-medium text-gray-700">
                  应用ID (AID) <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="aid"
                  value={config.aid || ''}
                  onChange={(e) => setConfig(prev => ({ ...prev, aid: e.target.value }))}
                  placeholder="请输入应用ID"
                  className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* 安全配置 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="h-4 w-4 text-amber-600" />
              <h4 className="font-medium text-gray-900">安全认证配置</h4>
            </div>
            <div className="space-y-2">
              <Label htmlFor="appSecret" className="text-sm font-medium text-gray-700">
                应用密钥 (APP_SECRET) <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  id="appSecret"
                  type={showPassword ? "text" : "password"}
                  value={config.appSecret || ''}
                  onChange={(e) => setConfig((prev: SystemConfigData) => ({ ...prev, appSecret: e.target.value }))}
                  placeholder="请输入应用密钥"
                  className="pr-12 transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-8 w-8 p-0 hover:bg-gray-100 rounded-md"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          <Separator />

          {/* 回调配置 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Globe className="h-4 w-4 text-purple-600" />
              <h4 className="font-medium text-gray-900">回调配置</h4>
            </div>
            <div className="space-y-2">
              <Label htmlFor="callbackUrl" className="text-sm font-medium text-gray-700">
                回调地址 (可选)
              </Label>
              <Input
                id="callbackUrl"
                value={config.callbackUrl || ''}
                onChange={(e) => setConfig(prev => ({ ...prev, callbackUrl: e.target.value }))}
                placeholder="https://your-domain.com/webhook/callback"
                className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-sm text-gray-500 flex items-start gap-2">
                <AlertCircle className="h-4 w-4 mt-0.5 text-blue-500 flex-shrink-0" />
                用于接收外部服务回调通知的HTTP/HTTPS地址，支持Webhook推送
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 数据映射配置区域 */}
      <Card className="shadow-sm border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <Database className="h-6 w-6 text-indigo-600" />
            </div>
            <div>
              <CardTitle className="text-xl font-semibold text-gray-900">
                数据映射配置
              </CardTitle>
              <p className="text-gray-600 mt-1">
                配置系统与外部API的数据字段映射关系，支持表格和JSON两种编辑模式
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="region" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="region" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                地区编号映射
              </TabsTrigger>
              <TabsTrigger value="service" className="flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                业务类型维护
              </TabsTrigger>
              <TabsTrigger value="policy" className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                优惠政策维护
              </TabsTrigger>
            </TabsList>

            {/* 地区编号映射 */}
            <TabsContent value="region" className="mt-0">
              <MappingConfigCard
                title="地区编号映射"
                icon={MapPin}
                mappings={regionMappingItems}
                setMappings={setRegionMappingItems}
                newItem={{ name: newRegion.region, code: newRegion.code }}
                setNewItem={(item) => {
                  if (typeof item === 'function') {
                    setNewRegion(prev => {
                      const currentItem = { name: prev.region, code: prev.code };
                      const newItem = item(currentItem);
                      return { region: newItem.name, code: newItem.code };
                    });
                  } else {
                    setNewRegion({ region: item.name, code: item.code });
                  }
                }}
                editingIndex={editingIndex}
                setEditingIndex={setEditingIndex}
                nameLabel="地区名称"
                codeLabel="地区编号"
                namePlaceholder="如：北京"
                codePlaceholder="如：1100"
                jsonPlaceholder='{"北京": "1100", "上海": "3100", "广东": "4400"}'
                onAdd={onAddRegionMapping}
                onDelete={onDeleteRegionMapping}
                onEdit={onEditRegionMapping}
                onSave={(index, name, code) => onSaveRegionMapping(index, name, code)}
              />
            </TabsContent>

            {/* 特定业务类型映射 */}
            <TabsContent value="service" className="mt-0">
              <MappingConfigCard
                title="特定业务类型维护"
                icon={Briefcase}
                mappings={serviceTypeMappingItems}
                setMappings={setServiceTypeMappingItems}
                newItem={{ name: newServiceType.name, code: newServiceType.code }}
                setNewItem={(item) => {
                  if (typeof item === 'function') {
                    setNewServiceType(prev => {
                      const currentItem = { name: prev.name, code: prev.code };
                      const newItem = item(currentItem);
                      return { name: newItem.name, code: newItem.code };
                    });
                  } else {
                    setNewServiceType({ name: item.name, code: item.code });
                  }
                }}
                editingIndex={editingServiceIndex}
                setEditingIndex={setEditingServiceIndex}
                nameLabel="业务名称"
                codeLabel="业务代码"
                namePlaceholder="如：成品油"
                codePlaceholder="如：2001"
                jsonPlaceholder='{"成品油": "2001", "不动产经营租赁服务": "2002", "货物运输服务": "2003", "旅客运输服务": "2004"}'
                presetItems={presetServiceTypes}
                presetColor="green"
                onAdd={onAddServiceTypeMapping}
                onDelete={onDeleteServiceTypeMapping}
                onEdit={onEditServiceTypeMapping}
                onSave={(index, name, code) => onSaveServiceTypeMapping(index, code, name)}
              />
            </TabsContent>

            {/* 优惠政策类型映射 */}
            <TabsContent value="policy" className="mt-0">
              <MappingConfigCard
                title="优惠政策类型维护"
                icon={AlertCircle}
                mappings={policyMappingItems}
                setMappings={setPolicyMappingItems}
                newItem={{ name: newPreferentialPolicy.name, code: newPreferentialPolicy.code }}
                setNewItem={(item) => {
                  if (typeof item === 'function') {
                    setNewPreferentialPolicy(prev => {
                      const currentItem = { name: prev.name, code: prev.code };
                      const newItem = item(currentItem);
                      return { name: newItem.name, code: newItem.code };
                    });
                  } else {
                    setNewPreferentialPolicy({ name: item.name, code: item.code });
                  }
                }}
                editingIndex={editingPolicyIndex}
                setEditingIndex={setEditingPolicyIndex}
                nameLabel="政策名称"
                codeLabel="政策代码"
                namePlaceholder="如：简易征收"
                codePlaceholder="如：3001"
                jsonPlaceholder='{"简易征收": "3001", "稀土产品": "3002", "免税": "3003", "不征税": "3004"}'
                presetItems={presetPolicyTypes}
                presetColor="purple"
                onAdd={onAddPreferentialPolicyMapping}
                onDelete={onDeletePreferentialPolicyMapping}
                onEdit={onEditPreferentialPolicyMapping}
                onSave={(index, name, code) => onSavePreferentialPolicyMapping(index, code, name)}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};