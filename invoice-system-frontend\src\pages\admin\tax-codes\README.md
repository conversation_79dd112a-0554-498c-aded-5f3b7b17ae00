# 税收编码管理模块

## 概述

税收编码管理模块已按照商家列表的格式和结构进行了完全重写，采用了组件化的架构设计，提供了更好的代码组织和维护性。

## 文件结构

```
tax-codes/
├── TaxCodeManagement.tsx          # 主组件
├── components/                    # 子组件目录
│   ├── taxCodeUtils.ts           # 工具函数和类型定义
│   ├── TaxCodeStatsCards.tsx     # 统计卡片组件
│   ├── TaxCodeFilters.tsx        # 筛选组件
│   ├── TaxCodeTableView.tsx      # 表格视图组件
│   ├── TaxCodeCardView.tsx       # 卡片视图组件
│   ├── TaxCodePagination.tsx     # 分页组件
│   ├── AddTaxCodeDialog.tsx      # 新增对话框
│   ├── ViewTaxCodeDialog.tsx     # 查看详情对话框
│   ├── EditTaxCodeDialog.tsx     # 编辑对话框
│   ├── DeleteTaxCodeDialog.tsx   # 删除确认对话框
│   └── AdvancedFilterDialog.tsx  # 高级筛选对话框
└── README.md                     # 说明文档
```

## 主要特性

### 1. 组件化架构
- 采用与商家列表相同的组件化结构
- 每个功能模块独立封装为组件
- 便于维护和扩展

### 2. 统一的UI风格
- 使用通用的StatsCards组件显示统计信息
- 使用通用的TableView和ListFilters组件
- 保持与系统其他模块的UI一致性

### 3. 完整的CRUD功能
- 新增税收编码
- 查看税收编码详情
- 编辑税收编码
- 删除税收编码
- 批量操作支持

### 4. 高级筛选功能
- 基本筛选：搜索、状态、类型
- 高级筛选：创建时间、更新时间范围
- 实时筛选和分页

### 5. 多视图支持
- 表格视图：详细信息展示
- 卡片视图：紧凑信息展示
- 响应式设计

## 数据结构

```typescript
interface TaxCode {
  id: number;
  categoryName: string;        // 编码名称
  categoryCode: string;        // 编码号
  categoryShortCode?: string;  // 简码
  isSummary: boolean;          // 是否为汇总项
  isActive: boolean;           // 是否启用
  shortName?: string;          // 简称
  description?: string;        // 说明
  keywords?: string;           // 关键字
  createdAt: string;          // 创建时间
  updatedAt: string;          // 更新时间
}
```

## API接口

- `GET /admin/tax-codes` - 获取税收编码列表
- `GET /admin/tax-codes/stats` - 获取统计信息
- `POST /admin/tax-codes` - 创建税收编码
- `PUT /admin/tax-codes/:id` - 更新税收编码
- `DELETE /admin/tax-codes/:id` - 删除税收编码

## 使用方法

```typescript
import TaxCodeManagement from '@/pages/admin/tax-codes/TaxCodeManagement';

// 在路由中使用
<Route path="/admin/tax-codes" component={TaxCodeManagement} />
```

## 依赖组件

- `@/components/common/StatsCards` - 统计卡片
- `@/components/common/TableView` - 表格视图
- `@/components/common/ListFilters` - 列表筛选
- `@/components/common/AdvancedFilterDialog` - 高级筛选
- `@/components/common/ToastManager` - 消息提示

## 注意事项

1. 确保后端API接口已正确实现
2. 需要配置正确的路由权限
3. 组件依赖shadcn/ui组件库
4. 使用了React Hooks，需要React 16.8+版本
