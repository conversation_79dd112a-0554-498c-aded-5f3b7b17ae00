import React from 'react';
import AdvancedFilterDialog from '@/components/common/AdvancedFilterDialog';
import { FilterField } from '@/components/common/AdvancedFilterDialog';

// 高级筛选条件接口
export interface AdvancedFilters {
  createdDateStart?: string;
  createdDateEnd?: string;
  updatedDateStart?: string;
  updatedDateEnd?: string;
}

interface TaxCodeAdvancedFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApply: (filters: AdvancedFilters) => void;
  initialFilters: AdvancedFilters;
}

// 筛选字段配置
const filterFields: FilterField[] = [
  {
    key: 'createdDateStart',
    label: '创建开始日期',
    type: 'date',
    placeholder: '选择开始日期',
  },
  {
    key: 'createdDateEnd',
    label: '创建结束日期',
    type: 'date',
    placeholder: '选择结束日期',
  },
  {
    key: 'updatedDateStart',
    label: '更新开始日期',
    type: 'date',
    placeholder: '选择开始日期',
  },
  {
    key: 'updatedDateEnd',
    label: '更新结束日期',
    type: 'date',
    placeholder: '选择结束日期',
  },
];

// 转换筛选条件格式
const convertToFilterValues = (filters: AdvancedFilters) => {
  return {
    createdDateStart: filters.createdDateStart || '',
    createdDateEnd: filters.createdDateEnd || '',
    updatedDateStart: filters.updatedDateStart || '',
    updatedDateEnd: filters.updatedDateEnd || '',
  };
};

const TaxCodeAdvancedFilterDialog: React.FC<TaxCodeAdvancedFilterDialogProps> = ({
  open,
  onOpenChange,
  onApply,
  initialFilters,
}) => {
  const handleApplyFilters = (newFilters: any) => {
    // 转换筛选条件格式以匹配现有的业务逻辑
    const transformedFilters: AdvancedFilters = {
      createdDateStart: newFilters.createdDateStart,
      createdDateEnd: newFilters.createdDateEnd,
      updatedDateStart: newFilters.updatedDateStart,
      updatedDateEnd: newFilters.updatedDateEnd,
    };

    onApply(transformedFilters);
  };

  return (
    <AdvancedFilterDialog
      open={open}
      onOpenChange={onOpenChange}
      onApply={handleApplyFilters}
      initialFilters={convertToFilterValues(initialFilters)}
      fields={filterFields}
      title='税收编码高级筛选'
      description='设置筛选条件来查找特定的税收编码'
      maxWidth='max-w-[500px]'
    />
  );
};

export default TaxCodeAdvancedFilterDialog;
