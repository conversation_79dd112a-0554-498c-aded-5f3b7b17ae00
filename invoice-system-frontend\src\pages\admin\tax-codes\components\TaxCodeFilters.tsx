import React from 'react';
import ListFilters from '@/components/common/ListFilters';
import { StatusOption, searchPlaceholders } from '@/components/common/ListFilters';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface TaxCodeFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  selectedStatus: string;
  setSelectedStatus: (value: string) => void;
  viewMode: 'table' | 'card';
  setViewMode: (mode: 'table' | 'card') => void;
  onAdvancedFilter: () => void;
}

// 税收编码状态选项
const taxCodeStatusOptions: StatusOption[] = [
  { value: 'all', label: '全部状态' },
  { value: 'true', label: '可用' },
  { value: 'false', label: '禁用' },
];

// 税收编码类型选项
const taxCodeTypeOptions: StatusOption[] = [
  { value: 'all', label: '全部类型' },
  { value: 'true', label: '汇总项' },
  { value: 'false', label: '明细项' },
];

const TaxCodeFilters: React.FC<TaxCodeFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  selectedStatus,
  setSelectedStatus,
  viewMode,
  setViewMode,
  onAdvancedFilter,
}) => {
  return (
    <ListFilters
      searchTerm={searchTerm}
      onSearchChange={setSearchTerm}
      searchPlaceholder="搜索编码名称、编号或关键字..."
      statusFilter={selectedStatus}
      onStatusFilterChange={setSelectedStatus}
      statusOptions={taxCodeStatusOptions}
      statusPlaceholder="状态筛选"
      viewMode={viewMode}
      onViewModeChange={setViewMode}
      onAdvancedFilter={onAdvancedFilter}
      advancedFilterText="高级筛选"
    />
  );
};

export default TaxCodeFilters;
