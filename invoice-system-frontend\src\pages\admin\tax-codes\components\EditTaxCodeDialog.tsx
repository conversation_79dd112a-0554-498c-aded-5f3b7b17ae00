import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card } from '@/components/ui/card';
import { Edit, Settings, FileText } from 'lucide-react';
import { TaxCode } from './taxCodeUtils';
import { apiService } from '@/services/base-api';
import { useToast } from '@/components/common/ToastManager';

interface EditTaxCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  taxCode: TaxCode | null;
  taxCodes: TaxCode[];
  onSuccess: () => void;
}

// 初始表单数据
const initialFormData = {
  categoryName: '',
  categoryCode: '',
  categoryShortCode: '',
  isSummary: false,
  isActive: true,
  shortName: '',
  description: '',
  keywords: '',
};

const EditTaxCodeDialog: React.FC<EditTaxCodeDialogProps> = ({
  open,
  onOpenChange,
  taxCode,
  taxCodes,
  onSuccess,
}) => {
  const { success, error } = useToast();
  const [formData, setFormData] = useState(initialFormData);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (taxCode) {
      setFormData({
        categoryName: taxCode.categoryName,
        categoryCode: taxCode.categoryCode,
        categoryShortCode: taxCode.categoryShortCode || '',
        isSummary: taxCode.isSummary,
        isActive: taxCode.isActive,
        shortName: taxCode.shortName || '',
        description: taxCode.description || '',
        keywords: taxCode.keywords || ''
      });
    } else {
      setFormData(initialFormData);
    }
  }, [taxCode]);

  const handleChange = (field: keyof typeof initialFormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!formData.categoryCode || !formData.categoryName || !taxCode) {
      error('请填写必填字段');
      return;
    }

    setLoading(true);
    try {
      const response = await apiService.put(`/admin/tax-codes/${taxCode.id}`, formData);
      if (response.success) {
        success('税收编码更新成功');
        onOpenChange(false);
        onSuccess();
      } else {
        throw new Error(response.error || '更新失败');
      }
    } catch (err) {
      error('更新失败', err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData(initialFormData);
    onOpenChange(false);
  };

  if (!taxCode) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4 pb-6 border-b">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-full bg-green-100">
              <Edit className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold">编辑税收编码</DialogTitle>
              <p className="text-muted-foreground mt-1">修改现有税收编码的详细信息</p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6 py-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5 text-blue-600" />
              基本信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="categoryCode" className="text-sm font-medium">
                  编号 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="categoryCode"
                  value={formData.categoryCode}
                  onChange={(e) => handleChange('categoryCode', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  disabled={true}
                  placeholder="例如: GST001"
                />
                <p className="text-xs text-muted-foreground">编码创建后不可修改</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryName" className="text-sm font-medium">
                  名称 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="categoryName"
                  value={formData.categoryName}
                  onChange={(e) => handleChange('categoryName', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  placeholder="例如: 标准税率"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryShortCode" className="text-sm font-medium">简码</Label>
                <Input
                  id="categoryShortCode"
                  value={formData.categoryShortCode}
                  onChange={(e) => handleChange('categoryShortCode', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  placeholder="例如: STD"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="shortName" className="text-sm font-medium">简称</Label>
                <Input
                  id="shortName"
                  value={formData.shortName}
                  onChange={(e) => handleChange('shortName', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  placeholder="例如: 标准"
                />
              </div>
            </div>
          </div>

          {/* 详细信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <FileText className="h-5 w-5 text-green-600" />
              详细信息
            </h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="keywords" className="text-sm font-medium">关键字</Label>
                <Input
                  id="keywords"
                  value={formData.keywords}
                  onChange={(e) => handleChange('keywords', e.target.value)}
                  placeholder="用逗号分隔关键字，例如: 标准,普通,一般"
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                />
                <p className="text-xs text-muted-foreground">关键字有助于快速搜索和分类</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">说明</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white min-h-[100px]"
                  placeholder="请输入详细说明..."
                />
              </div>
            </div>
          </div>

          {/* 配置选项 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5 text-purple-600" />
              配置选项
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4 border-purple-200 bg-purple-50">
                <div className="flex items-center gap-3">
                  <Switch
                    id="isSummary"
                    checked={formData.isSummary}
                    onCheckedChange={(c) => handleChange('isSummary', c)}
                    className="data-[state=checked]:bg-purple-600"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="isSummary" className="text-sm font-medium text-purple-900">设为汇总项</Label>
                    <p className="text-xs text-purple-700">汇总项可以包含多个子项目</p>
                  </div>
                </div>
              </Card>
              <Card className="p-4 border-green-200 bg-green-50">
                <div className="flex items-center gap-3">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(c) => handleChange('isActive', c)}
                    className="data-[state=checked]:bg-green-600"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="isActive" className="text-sm font-medium text-green-900">启用状态</Label>
                    <p className="text-xs text-green-700">启用后可在系统中使用</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-6 border-t">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            取消
          </Button>
          <Button
            onClick={handleSave}
            disabled={!formData.categoryCode || !formData.categoryName || loading}
            className="bg-green-600 hover:bg-green-700"
          >
            <Edit className="h-4 w-4 mr-2" />
            {loading ? '保存中...' : '保存修改'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditTaxCodeDialog;
