import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Plus,
  Trash2,
  Edit,
  Code,
  Table as TableIcon,
  AlertCircle,
  CheckCircle,
  LucideIcon
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface MappingItem {
  name: string;
  code: string;
}

interface MappingConfigCardProps {
  title: string;
  icon: LucideIcon;
  mappings: MappingItem[];
  setMappings: React.Dispatch<React.SetStateAction<MappingItem[]>>;
  newItem: MappingItem;
  setNewItem: React.Dispatch<React.SetStateAction<MappingItem>>;
  editingIndex: number | null;
  setEditingIndex: React.Dispatch<React.SetStateAction<number | null>>;
  nameLabel: string;
  codeLabel: string;
  namePlaceholder: string;
  codePlaceholder: string;
  jsonPlaceholder: string;
  presetItems?: Array<{ name: string; code: string }>;
  presetColor?: string;
  onAdd: () => void;
  onDelete: (index: number) => void;
  onEdit: (index: number) => void;
  onSave: (index: number, name: string, code: string) => void;
}

export const MappingConfigCard: React.FC<MappingConfigCardProps> = ({
  title,
  icon: Icon,
  mappings,
  setMappings,
  newItem,
  setNewItem,
  editingIndex,
  setEditingIndex,
  nameLabel,
  codeLabel,
  namePlaceholder,
  codePlaceholder,
  jsonPlaceholder,
  presetItems,
  presetColor = 'green',
  onAdd,
  onDelete,
  onEdit,
  onSave,
}) => {
  const [editMode, setEditMode] = useState<'table' | 'json'>('table');
  const [jsonText, setJsonText] = useState('');
  const [jsonError, setJsonError] = useState('');

  // 将数组转换为JSON字符串
  const convertToJson = () => {
    const jsonObject = mappings.reduce((acc, mapping) => {
      acc[mapping.name] = mapping.code;
      return acc;
    }, {} as Record<string, string>);
    return JSON.stringify(jsonObject, null, 2);
  };

  // 切换编辑模式
  const handleModeSwitch = () => {
    if (editMode === 'table') {
      setJsonText(convertToJson());
      setJsonError('');
      setEditMode('json');
    } else {
      if (applyJsonChanges()) {
        setEditMode('table');
      }
    }
  };

  // 应用JSON更改
  const applyJsonChanges = (): boolean => {
    try {
      const parsed = JSON.parse(jsonText);
      
      if (typeof parsed !== 'object' || parsed === null || Array.isArray(parsed)) {
        setJsonError(`JSON格式错误：必须是对象格式 {"${nameLabel}": "${codeLabel}"}`);
        return false;
      }

      const newMappings: MappingItem[] = Object.entries(parsed).map(([name, code]) => ({
        name,
        code: String(code)
      }));

      setMappings(newMappings);
      setJsonError('');
      toast({
        title: "JSON应用成功",
        description: `已更新 ${newMappings.length} 个${title}映射`,
      });
      return true;
    } catch (error) {
      setJsonError('JSON格式错误：' + (error as Error).message);
      return false;
    }
  };

  // 格式化JSON
  const formatJson = () => {
    try {
      const parsed = JSON.parse(jsonText);
      setJsonText(JSON.stringify(parsed, null, 2));
      setJsonError('');
      toast({
        title: "格式化成功",
        description: "JSON已格式化",
      });
    } catch (error) {
      setJsonError('JSON格式错误：' + (error as Error).message);
    }
  };

  return (
    <div className="space-y-4">
      {/* 编辑模式切换 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">{title}</span>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleModeSwitch}
          className="h-8"
        >
          {editMode === 'table' ? (
            <>
              <Code className="h-3 w-3 mr-1" />
              JSON编辑
            </>
          ) : (
            <>
              <TableIcon className="h-3 w-3 mr-1" />
              表格编辑
            </>
          )}
        </Button>
      </div>

      <div className="space-y-4">
        {editMode === 'table' ? (
          <>
            {/* 添加新映射 */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex gap-2 items-end">
                <div className="flex-1">
                  <Label htmlFor={`new${nameLabel}`} className="text-xs font-medium text-gray-600">
                    {nameLabel}
                  </Label>
                  <Input
                    id={`new${nameLabel}`}
                    value={newItem.name || ''}
                    onChange={(e) => setNewItem(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={namePlaceholder}
                    className="h-8 text-sm"
                  />
                </div>
                <div className="flex-1">
                  <Label htmlFor={`new${codeLabel}`} className="text-xs font-medium text-gray-600">
                    {codeLabel}
                  </Label>
                  <Input
                    id={`new${codeLabel}`}
                    value={newItem.code || ''}
                    onChange={(e) => setNewItem(prev => ({ ...prev, code: e.target.value }))}
                    placeholder={codePlaceholder}
                    className="h-8 text-sm"
                  />
                </div>
                <Button onClick={onAdd} size="sm" className="h-8">
                  <Plus className="h-3 w-3 mr-1" />
                  添加
                </Button>
              </div>
            </div>

            {/* 映射表格 */}
            <div className="border rounded-lg max-h-80 overflow-y-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-white">
                  <TableRow>
                    <TableHead className="h-10 text-xs">{nameLabel}</TableHead>
                    <TableHead className="h-10 text-xs">{codeLabel}</TableHead>
                    <TableHead className="w-20 h-10 text-xs">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mappings.map((mapping, index) => (
                    <TableRow key={index} className="h-10">
                      <TableCell className="py-2">
                        {editingIndex === index ? (
                          <Input
                            value={mapping.name || ''}
                            onChange={(e) => {
                              const newMappings = [...mappings];
                              newMappings[index].name = e.target.value;
                              setMappings(newMappings);
                            }}
                            onBlur={() => onSave(index, mapping.name, mapping.code)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                onSave(index, mapping.name, mapping.code);
                              }
                            }}
                            autoFocus
                            className="h-7 text-sm"
                          />
                        ) : (
                          <span
                            onClick={() => onEdit(index)}
                            className="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded text-sm block"
                          >
                            {mapping.name}
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="py-2">
                        {editingIndex === index ? (
                          <Input
                            value={mapping.code || ''}
                            onChange={(e) => {
                              const newMappings = [...mappings];
                              newMappings[index].code = e.target.value;
                              setMappings(newMappings);
                            }}
                            onBlur={() => onSave(index, mapping.name, mapping.code)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                onSave(index, mapping.name, mapping.code);
                              }
                            }}
                            className="h-7 text-sm"
                          />
                        ) : (
                          <span
                            onClick={() => onEdit(index)}
                            className="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded text-sm block font-mono"
                          >
                            {mapping.code}
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="py-2">
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEdit(index)}
                            className="h-6 w-6 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDelete(index)}
                            className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {mappings.length === 0 && (
                <div className="text-center py-6 text-gray-500 text-sm">
                  <div className="flex flex-col items-center gap-2">
                    <Icon className="h-8 w-8 text-gray-300" />
                    <p>暂无{title}映射，请添加</p>
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          <>
            {/* JSON编辑模式 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="jsonEditor" className="text-sm font-medium">JSON格式编辑</Label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={formatJson} className="h-7 text-xs">
                    格式化
                  </Button>
                  <Button size="sm" onClick={applyJsonChanges} className="h-7 text-xs">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    应用更改
                  </Button>
                </div>
              </div>

              <Textarea
                id="jsonEditor"
                value={jsonText}
                onChange={(e) => {
                  setJsonText(e.target.value);
                  setJsonError('');
                }}
                placeholder={jsonPlaceholder}
                rows={8}
                className={`font-mono text-xs ${jsonError ? 'border-red-500' : ''}`}
              />

              {jsonError && (
                <div className="flex items-start gap-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="text-red-700">
                    <p className="font-medium">JSON格式错误</p>
                    <p>{jsonError}</p>
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {/* 预设数据 */}
        {presetItems && presetItems.length > 0 && (
          <div className={`text-xs text-${presetColor}-600 bg-${presetColor}-50 p-2 rounded`}>
            <p className="font-medium mb-1">预设{title}：</p>
            <div className="grid grid-cols-2 gap-1">
              {presetItems.slice(0, 6).map((item, index) => (
                <p key={index} className="truncate">• {item.name} - {item.code}</p>
              ))}
              {presetItems.length > 6 && (
                <p className="text-gray-500">... 等 {presetItems.length} 项</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};