import React from 'react';
import StatsCards from '@/components/common/StatsCards';
import { Package, Check, XSquare, Layers } from 'lucide-react';
import { StatsCardConfig } from '@/components/common/StatsCards';
import { TaxCodeStats } from './taxCodeUtils';

interface TaxCodeStatsCardsProps {
  stats: TaxCodeStats;
  loading?: boolean;
}

// 税收编码统计卡片配置
const taxCodeStatsConfigs: StatsCardConfig[] = [
  {
    key: 'total',
    label: '总编码数',
    icon: Package,
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-600',
  },
  {
    key: 'active',
    label: '可用编码',
    icon: Check,
    bgColor: 'bg-green-50',
    iconColor: 'text-green-600',
  },
  {
    key: 'inactive',
    label: '禁用编码',
    icon: XSquare,
    bgColor: 'bg-red-50',
    iconColor: 'text-red-600',
  },
  {
    key: 'summary',
    label: '汇总项',
    icon: Layers,
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-600',
  },
];

const TaxCodeStatsCards: React.FC<TaxCodeStatsCardsProps> = ({ stats, loading = false }) => {
  return <StatsCards stats={stats as any} cardConfigs={taxCodeStatsConfigs} loading={loading} />;
};

export default TaxCodeStatsCards;
