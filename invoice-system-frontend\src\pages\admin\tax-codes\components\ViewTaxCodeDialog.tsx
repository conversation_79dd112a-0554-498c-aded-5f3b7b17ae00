import React from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Eye, Edit, Package, FileText, Activity, Clock, Calendar, Layers, Check, XSquare } from 'lucide-react';
import { TaxCode } from './taxCodeUtils';

interface ViewTaxCodeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  taxCode: TaxCode | null;
  onEdit: (taxCode: TaxCode) => void;
}

export const ViewTaxCodeDialog: React.FC<ViewTaxCodeDialogProps> = ({
  isOpen,
  onClose,
  taxCode,
  onEdit,
}) => {
  if (!taxCode) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4 pb-6 border-b">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <Eye className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold">税收编码详情</DialogTitle>
              <p className="text-muted-foreground mt-1">查看税收编码的详细信息</p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6 py-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Package className="h-5 w-5 text-blue-600" />
              基本信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">编号</Label>
                  <div className="font-mono text-lg font-bold text-blue-700 bg-blue-50 px-3 py-2 rounded-md">
                    {taxCode.categoryCode}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">名称</Label>
                  <div className="text-lg font-semibold text-gray-900">
                    {taxCode.categoryName}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">简码</Label>
                  <div className="text-base text-gray-700">
                    {taxCode.categoryShortCode || <span className="text-muted-foreground">未设置</span>}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">简称</Label>
                  <div className="text-base text-gray-700">
                    {taxCode.shortName || <span className="text-muted-foreground">未设置</span>}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          {/* 详细信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <FileText className="h-5 w-5 text-green-600" />
              详细信息
            </h3>
            <div className="space-y-4">
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">关键字</Label>
                  <div>
                    {taxCode.keywords ? (
                      <div className="flex flex-wrap gap-2">
                        {taxCode.keywords.split(',').map((keyword, index) => (
                          <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-700">
                            {keyword.trim()}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">未设置关键字</span>
                    )}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">说明</Label>
                  <div className="text-gray-700 min-h-[60px] whitespace-pre-wrap">
                    {taxCode.description || <span className="text-muted-foreground">未添加说明</span>}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          {/* 状态信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-600" />
              状态信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className={`p-4 border-2 ${taxCode.isSummary ? 'border-purple-200 bg-purple-50' : 'border-gray-200 bg-gray-50'}`}>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${taxCode.isSummary ? 'bg-purple-100' : 'bg-gray-100'}`}>
                    {taxCode.isSummary ? (
                      <Layers className="h-5 w-5 text-purple-600" />
                    ) : (
                      <FileText className="h-5 w-5 text-gray-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {taxCode.isSummary ? '汇总项' : '明细项'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {taxCode.isSummary ? '此编码为汇总项目' : '此编码为明细项目'}
                    </p>
                  </div>
                </div>
              </Card>
              <Card className={`p-4 border-2 ${taxCode.isActive ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${taxCode.isActive ? 'bg-green-100' : 'bg-red-100'}`}>
                    {taxCode.isActive ? (
                      <Check className="h-5 w-5 text-green-600" />
                    ) : (
                      <XSquare className="h-5 w-5 text-red-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {taxCode.isActive ? '已启用' : '已禁用'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {taxCode.isActive ? '此编码可在系统中使用' : '此编码已被禁用'}
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Clock className="h-5 w-5 text-gray-600" />
              时间信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">创建时间</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(taxCode.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900">更新时间</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(taxCode.updatedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
          <Button onClick={() => onEdit(taxCode)} className="bg-blue-600 hover:bg-blue-700">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


