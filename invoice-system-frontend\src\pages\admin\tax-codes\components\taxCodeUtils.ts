// 税收编码数据结构
export interface TaxCode {
  id: number;
  categoryName: string;
  categoryCode: string;
  categoryShortCode?: string;
  isSummary: boolean;
  isActive: boolean;
  shortName?: string;
  description?: string;
  keywords?: string;
  createdAt: string;
  updatedAt: string;
}

// 税收编码统计数据接口
export interface TaxCodeStats {
  total: number;
  active: number;
  inactive: number;
  summary: number;
}

// 获取初始统计数据
export const getInitialStats = (): TaxCodeStats => ({
  total: 0,
  active: 0,
  inactive: 0,
  summary: 0,
});

// 格式化时间显示
export const formatRegistrationTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

export const formatDetailedTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 格式化显示函数
export const formatPhoneDisplay = (phone: string): string => {
  if (!phone) return '-';
  // 隐藏中间4位数字
  if (phone.length === 11) {
    return `${phone.slice(0, 3)}****${phone.slice(7)}`;
  }
  return phone;
};

export const formatEmailDisplay = (email: string): string => {
  if (!email) return '-';
  const [username, domain] = email.split('@');
  if (username && domain) {
    const hiddenUsername = username.length > 2 
      ? `${username.slice(0, 2)}***${username.slice(-1)}`
      : username;
    return `${hiddenUsername}@${domain}`;
  }
  return email;
};
