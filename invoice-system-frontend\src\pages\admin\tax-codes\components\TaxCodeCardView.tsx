import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, Edit, Trash2, Package, Layers, FileText, Check, XSquare } from 'lucide-react';
import { TaxCode } from './taxCodeUtils';

interface TaxCodeCardViewProps {
  taxCodes: TaxCode[];
  onView: (taxCode: TaxCode) => void;
  onEdit: (taxCode: TaxCode) => void;
  onDelete: (taxCode: TaxCode) => void;
}

const TaxCodeCardView: React.FC<TaxCodeCardViewProps> = ({
  taxCodes,
  onView,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {taxCodes.map((taxCode) => (
        <Card key={taxCode.id} className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-4">
            <div className="space-y-3">
              {/* 头部信息 */}
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="font-mono text-sm font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md inline-block">
                    {taxCode.categoryCode}
                  </div>
                  <h3 className="font-medium text-gray-900 text-sm" title={taxCode.categoryName}>
                    {taxCode.categoryName}
                  </h3>
                </div>
                <div className="text-xs text-gray-500">
                  ID: {taxCode.id}
                </div>
              </div>

              {/* 状态和类型 */}
              <div className="flex items-center gap-2">
                <Badge
                  variant={taxCode.isSummary ? 'default' : 'secondary'}
                  className={`text-xs ${
                    taxCode.isSummary
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-blue-100 text-blue-700'
                  }`}
                >
                  <div className="flex items-center gap-1">
                    {taxCode.isSummary ? <Layers className="h-3 w-3" /> : <FileText className="h-3 w-3" />}
                    {taxCode.isSummary ? '汇总项' : '明细项'}
                  </div>
                </Badge>
                <Badge
                  variant={taxCode.isActive ? 'default' : 'destructive'}
                  className={`text-xs ${
                    taxCode.isActive
                      ? 'bg-green-100 text-green-700'
                      : 'bg-red-100 text-red-700'
                  }`}
                >
                  <div className="flex items-center gap-1">
                    {taxCode.isActive ? <Check className="h-3 w-3" /> : <XSquare className="h-3 w-3" />}
                    {taxCode.isActive ? '可用' : '禁用'}
                  </div>
                </Badge>
              </div>

              {/* 简称 */}
              {taxCode.shortName && (
                <div className="text-xs text-muted-foreground bg-gray-100 px-2 py-1 rounded-full inline-block">
                  简称: {taxCode.shortName}
                </div>
              )}

              {/* 关键字 */}
              {taxCode.keywords && (
                <div className="space-y-1">
                  <div className="text-xs text-gray-600">关键字:</div>
                  <div className="flex flex-wrap gap-1">
                    {taxCode.keywords.split(',').slice(0, 3).map((keyword, index) => (
                      <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full">
                        {keyword.trim()}
                      </span>
                    ))}
                    {taxCode.keywords.split(',').length > 3 && (
                      <span className="text-xs text-muted-foreground">+{taxCode.keywords.split(',').length - 3}</span>
                    )}
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex items-center justify-end gap-1 pt-2 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onView(taxCode)}
                  className="h-8 w-8 p-0 text-gray-500 hover:text-blue-600"
                  title="查看详情"
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(taxCode)}
                  className="h-8 w-8 p-0 text-gray-500 hover:text-green-600"
                  title="编辑"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(taxCode)}
                  className="h-8 w-8 p-0 text-gray-500 hover:text-red-600"
                  title="删除"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default TaxCodeCardView;
