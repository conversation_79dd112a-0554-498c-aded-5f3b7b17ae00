import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Trash2, Alert<PERSON>riangle } from 'lucide-react';
import { TaxCode } from './taxCodeUtils';
import { apiService } from '@/services/base-api';
import { useToast } from '@/components/common/ToastManager';

interface DeleteTaxCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  taxCode: TaxCode | null;
  onSuccess: () => void;
}

export const DeleteTaxCodeDialog: React.FC<DeleteTaxCodeDialogProps> = ({
  open,
  onOpenChange,
  taxCode,
  onSuccess,
}) => {
  const { success, error } = useToast();
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!taxCode) return;

    setLoading(true);
    try {
      const response = await apiService.delete(`/admin/tax-codes/${taxCode.id}`);
      if (response.success) {
        success('税收编码删除成功');
        onOpenChange(false);
        onSuccess();
      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (err) {
      error('删除失败', err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  if (!taxCode) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <Trash2 className="h-8 w-8 text-red-600" />
          </div>
          <div>
            <DialogTitle className="text-xl font-bold text-gray-900">
              删除税收编码
            </DialogTitle>
            <p className="text-muted-foreground mt-2">
              此操作不可撤销，请确认您的选择
            </p>
          </div>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* 要删除的编码信息 */}
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-red-900">编码:</span>
                  <span className="font-mono text-sm font-bold text-red-700">{taxCode.categoryCode}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-red-900">名称:</span>
                  <span className="text-sm text-red-700">{taxCode.categoryName}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="text-center">
            <p className="text-gray-700">
              您确定要删除这个税收编码吗？
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              删除后数据将无法恢复
            </p>
          </div>

          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="font-medium text-amber-900">重要提醒</p>
                  <p className="text-sm text-amber-800">
                    删除此税收编码可能会影响已使用此编码的发票、报表和其他相关数据。
                  </p>
                  <p className="text-sm text-amber-800 font-medium">
                    请确保在删除前已做好相应的数据备份和处理。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-3">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1"
            disabled={loading}
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            className="flex-1 bg-red-600 hover:bg-red-700"
            disabled={loading}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {loading ? '删除中...' : '确认删除'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


